import random
import math

#import warnings
#warnings.filterwarnings("ignore")

from typing import List

import torch
import numpy as np
from datasets import load_dataset
from transformers import AutoTokenizer, AutoModel
from sentence_transformers import util
from tqdm import tqdm


class NDCGCalculator:
    def __init__(
        self,
        model_name: str = "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2",
    ):
        """
        Initialise le calculateur nDCG avec un modèle d'embeddings multilingue

        Args:
            model_name: Nom du modèle HuggingFace pour les embeddings
        """
        self.model_name = model_name
        self.tokenizer = AutoTokenizer.from_pretrained(model_name)
        self.model = AutoModel.from_pretrained(model_name)
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.model.to(self.device)
        self.model.eval()

        print(f"Modèle chargé: {model_name}")
        print(f"Device utilisé: {self.device}")

    def encode_texts(self, texts: List[str], batch_size: int = 32) -> np.ndarray:
        """
        Encode une liste de textes en embeddings

        Args:
            texts: Liste des textes à encoder
            batch_size: Taille du batch pour l'encodage

        Returns:
            Matrice numpy des embeddings
        """
        embeddings = []

        for i in tqdm(range(0, len(texts), batch_size), desc="Encodage des textes"):
            batch_texts = texts[i : i + batch_size]

            # Tokenisation
            inputs = self.tokenizer(
                batch_texts,
                padding=True,
                truncation=True,
                max_length=512,
                return_tensors="pt",
            ).to(self.device)

            # Génération des embeddings
            with torch.no_grad():
                outputs = self.model(**inputs)
                # Mean pooling
                embeddings_batch = outputs.last_hidden_state.mean(dim=1)
                embeddings_batch = embeddings_batch.cpu().numpy()
                embeddings.append(embeddings_batch)

        return np.vstack(embeddings)

    def dcg_at_k(self, relevances: List[float], k: int) -> float:
        """
        Calcule le DCG@k (Discounted Cumulative Gain)

        Args:
            relevances: Liste des scores de pertinence ordonnés
            k: Nombre de documents à considérer

        Returns:
            Score DCG@k
        """
        relevances = relevances[:k]
        dcg = 0.0
        for i, rel in enumerate(relevances):
            dcg += rel / math.log2(i + 2)  # i+2 car l'indice commence à 0
        return dcg

    def ndcg_at_k(self, relevances: List[float], k: int) -> float:
        """
        Calcule le nDCG@k (Normalized Discounted Cumulative Gain)

        Args:
            relevances: Liste des scores de pertinence ordonnés
            k: Nombre de documents à considérer

        Returns:
            Score nDCG@k normalisé entre 0 et 1
        """
        dcg = self.dcg_at_k(relevances, k)

        # IDCG: DCG idéal (relevances triées par ordre décroissant)
        ideal_relevances = sorted(relevances, reverse=True)
        idcg = self.dcg_at_k(ideal_relevances, k)

        if idcg == 0:
            return 0.0

        return dcg / idcg

    def calculate_similarities(
        self, query_embedding: np.ndarray, passage_embeddings: np.ndarray
    ) -> np.ndarray:
        """
        Calcule les similarités cosinus entre une requête et des passages

        Args:
            query_embedding: Embedding de la requête
            passage_embeddings: Embeddings des passages

        Returns:
            Array des similarités cosinus
        """
        # Conversion en tenseurs PyTorch si nécessaire
        query_tensor = torch.tensor(query_embedding).unsqueeze(0)
        passage_tensor = torch.tensor(passage_embeddings)

        # Calcul des similarités cosinus avec sentence-transformers
        similarities = util.cos_sim(query_tensor, passage_tensor)[0]

        return similarities.numpy()

    def evaluate_triplet(
        self,
        query: str,
        positive: str,
        negative: str,
        additional_negatives: List[str] = None,
        k: int = 10,
    ) -> float:
        """
        Évalue un triplet et calcule le nDCG@k

        Args:
            query: Texte de la requête
            positive: Passage pertinent
            negative: Passage non pertinent
            additional_negatives: Passages additionnels non pertinents
            k: Nombre de documents à considérer

        Returns:
            Score nDCG@k
        """
        # Construction de la liste des passages
        passages = [positive, negative]
        relevance_labels = [1, 0]  # 1 pour pertinent, 0 pour non pertinent

        # Ajout de passages négatifs supplémentaires si fournis
        if additional_negatives:
            passages.extend(additional_negatives)
            relevance_labels.extend([0] * len(additional_negatives))

        # Encodage de la requête
        query_embedding = self.encode_texts([query])

        # Encodage des passages
        passage_embeddings = self.encode_texts(passages)

        # Calcul des similarités
        similarities = self.calculate_similarities(
            query_embedding[0], passage_embeddings
        )

        # Tri par similarité décroissante
        sorted_indices = np.argsort(similarities)[::-1]

        # Récupération des labels de pertinence dans l'ordre trié
        sorted_relevances = [relevance_labels[i] for i in sorted_indices]

        # Calcul du nDCG@k
        ndcg_score = self.ndcg_at_k(sorted_relevances, k)

        return ndcg_score


def load_mmarco_french_data(num_queries: int = 100):
    """
    Charge les données mmarco en français (https://huggingface.co/datasets/unicamp-dl/mmarco)

    Args:
        num_queries: Nombre de requêtes à traiter (pour limiter les tests)

    Returns:
        Liste de triplets (query, positive_passage, negative_passage)
    """
    print("Chargement du dataset mmarco français...")

    dataset = load_dataset("unicamp-dl/mmarco", "french", split="train")

    print(f"Dataset chargé: {len(dataset)} triplets d'entraînement")

    if num_queries > 0 and num_queries < len(dataset):
        dataset = dataset.select(range(num_queries))
        print(f"Dataset limité à {num_queries} triplets")

    return dataset


def main():
    """
    Fonction principale pour calculer le nDCG@10 sur mmarco français
    """
    calculator = NDCGCalculator()

    dataset = load_mmarco_french_data(num_queries=200)

    ndcg_scores = []

    print(f"\nÉvaluation sur {len(dataset)} triplets français...")

    # Génération de passages négatifs supplémentaires à partir du dataset
    # pour augmenter la difficulté de l'évaluation
    all_negatives = [item["negative"] for item in dataset]

    for i, item in enumerate(tqdm(dataset, desc="Évaluation des triplets")):
        query = item["query"]
        positive = item["positive"]
        negative = item["negative"]

        # Sélection de passages négatifs supplémentaires (échantillonnage aléatoire)
        random.seed(42 + i)
        additional_negatives = random.sample(
            [neg for neg in all_negatives if neg != negative],
            min(8, len(all_negatives) - 1),  # Max 8 négatifs supplémentaires
        )

        try:
            ndcg_score = calculator.evaluate_triplet(
                query, positive, negative, additional_negatives, k=10
            )
            ndcg_scores.append(ndcg_score)

            if i % 10 == 0:
                print(f"Triplet {i + 1}: nDCG@10 = {ndcg_score:.4f}")
        except Exception as e:
            print(f"Erreur pour le triplet {i + 1}: {e}")
            continue

    if ndcg_scores:
        mean_ndcg = np.mean(ndcg_scores)
        std_ndcg = np.std(ndcg_scores)

        print("\n=== Résultats finaux ===")
        print(f"Nombre de triplets évalués: {len(ndcg_scores)}")
        print(f"nDCG@10 moyen: {mean_ndcg:.4f} ± {std_ndcg:.4f}")
        print(f"nDCG@10 médian: {np.median(ndcg_scores):.4f}")
        print(f"nDCG@10 min: {np.min(ndcg_scores):.4f}")
        print(f"nDCG@10 max: {np.max(ndcg_scores):.4f}")

        # Analyse de la performance
        print("\n=== Analyse ===")
        perfect_scores = sum(1 for score in ndcg_scores if score >= 0.99)
        good_scores = sum(1 for score in ndcg_scores if score >= 0.7)
        print(
            f"Scores parfaits (≥0.99): {perfect_scores}/{len(ndcg_scores)} ({perfect_scores / len(ndcg_scores) * 100:.1f}%)"
        )
        print(
            f"Bons scores (≥0.70): {good_scores}/{len(ndcg_scores)} ({good_scores / len(ndcg_scores) * 100:.1f}%)"
        )
    else:
        print("Aucun triplet n'a pu être évalué.")


if __name__ == "__main__":
    main()

    """
    dataset = load_dataset("unicamp-dl/mmarco", "runs-french")
    print(dataset)
    print(len(dataset['bm25'][0]['passages']['passage']))
    print(len(dataset['bm25'][0]['passages']['id']))
    """

