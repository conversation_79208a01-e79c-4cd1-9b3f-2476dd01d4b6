
import numpy as np

from datasets import load_dataset

from sentence_transformers import SentenceTransformer, util
from tqdm import tqdm


# https://huggingface.co/spaces/french-datasets/french-datasets

def dcg_at_k(relevances, k=10):
    relevances = np.asfarray(relevances)[:k]
    if relevances.size:
        return np.sum((2**relevances - 1) / np.log2(np.arange(2, relevances.size + 2)))
    return 0.0


def ndcg_at_k(relevances, k=10):
    dcg = dcg_at_k(relevances, k)
    ideal_dcg = dcg_at_k(sorted(relevances, reverse=True), k)
    return dcg / ideal_dcg if ideal_dcg > 0 else 0.0



if __name__ == "__main__":
    print("=== Évaluation nDCG@10 sur MMARCO (FR) ===")

    print("Chargement du Dataset MMARCO (FR)...")
    # https://huggingface.co/datasets/unicamp-dl/mmarco
    # name = french, collection-french, queries-french, runs-french
    ds_fr = load_dataset("unicamp-dl/mmarco", "french", trust_remote_code=True)
    subset_ds_fr = ds_fr['train'].take(200) # type: ignore

    print(subset_ds_fr)

    print("Chargement du modèle d'embeddings...")
    # https://huggingface.co/intfloat/multilingual-e5-base
    model = SentenceTransformer("intfloat/multilingual-e5-base")
    # https://huggingface.co/google/mt5-large
    # google/mt5-large
    # https://huggingface.co/sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2
    # paraphrase-multilingual-mpnet-base-v2
    # https://huggingface.co/sentence-transformers/paraphrase-multilingual-mpnet-base-v2
    # BAAI/bge-multilingual-gemma2
    # https://huggingface.co/BAAI/bge-multilingual-gemma2

    scores = []
    for sample in tqdm(subset_ds_fr, desc="Évaluation"):
        query = sample["query"] # type: ignore
        pos_doc = sample["positive"] # type: ignore
        neg_doc = sample["negative"] # type: ignore
        all_docs = pos_doc + neg_doc

        q_emb = model.encode(query, convert_to_tensor=True)
        d_embs = model.encode(all_docs, convert_to_tensor=True)

        scores_docs = util.cos_sim(q_emb, d_embs)[0].cpu().numpy()