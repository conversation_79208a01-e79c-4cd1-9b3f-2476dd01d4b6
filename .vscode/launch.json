{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "eval_ndcg_mmarco_fr_embeddings",
            "type": "debugpy",
            "request": "launch",
            "program": "eval_ndcg_mmarco_fr_embeddings.py",
            "cwd": "${workspaceFolder}",
            "args": [],
            "justMyCode": false
        },
        {
            "name": "ndcg_mmarco_calculator",
            "type": "debugpy",
            "request": "launch",
            "program": "ndcg_mmarco_calculator.py",
            "cwd": "${workspaceFolder}",
            "args": [],
            "justMyCode": false
        },
        {
            "name": "Current File",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "cwd": "${workspaceFolder}",
            "args": [],
            "justMyCode": false
        },
    ],
}
