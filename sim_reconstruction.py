from sentence_transformers import SentenceTransformer, util

def sentence_bert_similarity_fr(original_sentence: str, reconstruction_sentence: str) -> float:
    """
    Calcule la similarité cosinus entre deux phrases en utilisant un modèle Sentence-BERT adapté au français.

    Args:
        original_sentence (str): La phrase de référence.
        reconstruction_sentence (str): La phrase reconstruite à évaluer.

    Returns:
        float: Le score de similarité cosinus (entre -1 et 1), où 1 indique une similarité parfaite.
    """
    # Chargez le modèle Sentence-BERT pré-entraîné pour le français.
    # 'dangvantuan/sentence-camembert-base' est un modèle robuste et spécifique au français.
    # 'paraphrase-multilingual-MiniLM-L12-v2' est une excellente alternative multilingue.
    try:
        # https://huggingface.co/sentence-transformers/paraphrase-multilingual-mpnet-base-v2
        #model = SentenceTransformer("paraphrase-multilingual-mpnet-base-v2")
        # https://huggingface.co/sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2
        #model = SentenceTransformer('paraphrase-multilingual-MiniLM-L12-v2')
        # https://huggingface.co/dangvantuan/sentence-camembert-base
        #model = SentenceTransformer('dangvantuan/sentence-camembert-base')

        # https://huggingface.co/intfloat/multilingual-e5-base
        #model = SentenceTransformer("intfloat/multilingual-e5-base")

        # https://huggingface.co/google/mt5-large
        ##model = SentenceTransformer("google/mt5-large")
        model_name = "google/mt5-small"
        # https://huggingface.co/BAAI/bge-multilingual-gemma2
        ##model = SentenceTransformer("BAAI/bge-multilingual-gemma2")

        from transformers import AutoTokenizer, AutoModel
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        model = AutoModel.from_pretrained(model_name)
        model.to("cuda")
        model.eval()
    except Exception as e:
        print(f"Erreur lors du chargement du modèle: {e}")
        exit(-1)

    embeddings1 = model.encode(original_sentence, convert_to_tensor=True)
    embeddings2 = model.encode(reconstruction_sentence, convert_to_tensor=True)

    cosine_score = util.pytorch_cos_sim(embeddings1, embeddings2)

    return cosine_score.item()

sentence = "Déprogrammations : TF1 et M6 bousculent leurs grilles à cause de l'Euro 2024"
reconstructions = [
    "L'Euro 2024 pousse TF1 et M6 à modifier leurs programmes habituels.",
    "Les chaînes TF1 et M6 adaptent leurs grilles de diffusion en raison de l'Euro 2024.",
    "Suite à l'Euro 2024, TF1 et M6 réorganisent leurs programmations.",
    "En raison de l'Euro 2024, les émissions de TF1 et M6 sont déprogrammées ou déplacées.",
    "TF1 et M6 chamboulent leurs plannings télévisuels à cause de l'Euro 2024.",
    "L'Euro 2024 est la cause des changements de grille pour TF1 et M6.",
    "Les grilles de TF1 et M6 sont impactées par l'Euro 2024, entraînant des déprogrammations.",
    "Pour l'Euro 2024, TF1 et M6 ont dû revoir intégralement leurs grilles de programmes.",
    "TF1 et M6 procèdent à des aménagements de leurs grilles en lien avec l'Euro 2024.",
    "Le calendrier de l'Euro 2024 force TF1 et M6 à remanier leurs diffusions."
]

print(f"Phrase originale : '{sentence}'\n")

for i, recon in enumerate(reconstructions):
    similarity = sentence_bert_similarity_fr(sentence, recon)
    print(f"Reconstruction {i+1}: '{recon}'")
    print(f"Score de pertinence (Similarité Cosinus) : {similarity:.4f}")
